// src/pages/Home.js

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Home = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in by checking for token
    const token = localStorage.getItem('token');
    setIsLoggedIn(!!token);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    setIsLoggedIn(false);
    navigate('/login');
  };

  return (
    <div className="home-container">
      <div className="intro-text">
        <h1>Welcome to the Future</h1>
        <p>Where innovation meets style</p>
      </div>

      {isLoggedIn ? (
        <div className="dashboard-action">
          <button className="futuristic-button" onClick={() => navigate('/dashboard')}>
            Enter Dashboard
          </button>
          <button className="futuristic-button logout" onClick={handleLogout}>
            Logout
          </button>
        </div>
      ) : (
        <div className="auth-action">
          <button className="futuristic-button" onClick={() => navigate('/login')}>
            Login
          </button>
          <button className="futuristic-button" onClick={() => navigate('/register')}>
            Register
          </button>
        </div>
      )}
    </div>
  );
};

export default Home;
