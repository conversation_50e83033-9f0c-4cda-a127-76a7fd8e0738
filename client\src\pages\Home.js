// src/components/HomePage.js

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const HomePage = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(true);  // Placeholder for auth state
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('token');
    setIsLoggedIn(false);
    navigate('/login');  // Navigate back to login after logging out
  };

  return (
    <div className="home-container">
      <div className="intro-text">
        <h1>Welcome to the Future</h1>
        <p>Where innovation meets style</p>
      </div>

      {isLoggedIn ? (
        <div className="dashboard-action">
          <button className="futuristic-button" onClick={() => navigate('/dashboard')}>
            Enter Dashboard
          </button>
          <button className="futuristic-button logout" onClick={handleLogout}>
            Logout
          </button>
        </div>
      ) : (
        <div className="auth-action">
          <button className="futuristic-button" onClick={() => navigate('/login')}>
            Login
          </button>
          <button className="futuristic-button" onClick={() => navigate('/register')}>
            Register
          </button>
        </div>
      )}
    </div>
  );
};

export default HomePage;
