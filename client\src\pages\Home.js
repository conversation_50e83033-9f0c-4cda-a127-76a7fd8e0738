// src/pages/Home.js

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Home = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in by checking for token
    const token = localStorage.getItem('token');
    setIsLoggedIn(!!token);

    // You could decode the token to get user info, for now using placeholder
    if (token) {
      setUserName('User'); // This could be extracted from token
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    setIsLoggedIn(false);
    setUserName('');
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main Heading */}
          <div className="animate-fade-in">
            <h1 className="text-5xl font-bold text-secondary-900 mb-6">
              Welcome to the
              <span className="text-primary-600" style={{display: 'block'}}>
                Future
              </span>
            </h1>
            <p className="text-xl text-secondary-600 mb-12 mx-auto" style={{maxWidth: '32rem'}}>
              Where innovation meets style. Experience the next generation of web applications.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="animate-slide-up">
            {isLoggedIn ? (
              <div className="space-y-6">
                <div className="card max-w-md mx-auto">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                      Welcome back, {userName}!
                    </h3>
                    <p className="text-secondary-600 mb-6">
                      Ready to continue your journey?
                    </p>
                    <div className="space-y-3">
                      <button
                        onClick={() => navigate('/dashboard')}
                        className="btn-primary w-full text-lg py-3"
                      >
                        Enter Dashboard
                      </button>
                      <button
                        onClick={handleLogout}
                        className="btn-secondary w-full"
                      >
                        Logout
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-8">
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <button
                    onClick={() => navigate('/login')}
                    className="btn-primary text-lg px-8 py-3 w-full sm:w-auto"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => navigate('/register')}
                    className="btn-secondary text-lg px-8 py-3 w-full sm:w-auto"
                  >
                    Get Started
                  </button>
                </div>

                {/* Features Grid */}
                <div className="grid md:grid-cols-3 gap-6 mt-16 max-w-4xl mx-auto">
                  <div className="card text-center hover:shadow-xl transition-shadow duration-300">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-secondary-900 mb-2">Lightning Fast</h3>
                    <p className="text-secondary-600">Built with modern technologies for optimal performance</p>
                  </div>

                  <div className="card text-center hover:shadow-xl transition-shadow duration-300">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-secondary-900 mb-2">Secure</h3>
                    <p className="text-secondary-600">Your data is protected with industry-standard security</p>
                  </div>

                  <div className="card text-center hover:shadow-xl transition-shadow duration-300">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-secondary-900 mb-2">User Friendly</h3>
                    <p className="text-secondary-600">Intuitive design that puts user experience first</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
