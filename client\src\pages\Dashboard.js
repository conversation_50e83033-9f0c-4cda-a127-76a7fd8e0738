import { useEffect, useState } from 'react';
import axios from 'axios';

const Dashboard = () => {
  const [message, setMessage] = useState('');
  const [userId, setuserId] = useState('');



  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem('token');
      if (!token) return;

      try {
        const response = await axios.get('http://localhost:1609/api/users/dashboard', {
          headers: { 'x-auth-token': token }
        });
        setMessage(response.data.message);
        setuserId(response.data.userId);
      } catch (error) {
        console.error(error.response.data.message);
      }
    };

    fetchData();
  }, []);

  return (
    <div>
      <h2>Dashboard</h2>
      <p>{message}, {userId}</p>
    </div>
  );
};

export default Dashboard;
